# 腾讯云K8s管理工具配置文件

# 默认配置
default:
  # 当前使用的集群配置名称
  current_cluster: "default"
  
  # 输出格式 (table, json, yaml)
  output_format: "table"
  
  # 是否显示详细信息
  verbose: false
  
  # 超时设置（秒）
  timeout: 30

# 集群配置
clusters:
  # 默认集群配置示例
  default:
    # 集群名称
    name: "tx-k8s-cluster"
    
    # kubeconfig文件路径（可选，如果不指定则使用默认路径）
    kubeconfig_path: "~/.kube/config"
    
    # 集群上下文名称（可选）
    context: ""
    
    # 命名空间（默认）
    namespace: "default"
    
    # 腾讯云相关配置
    tencent:
      # 地域
      region: "ap-beijing"
      
      # 集群ID
      cluster_id: ""
      
      # 访问密钥（建议使用环境变量）
      secret_id: ""
      secret_key: ""

# 监控配置
monitoring:
  # 刷新间隔（秒）
  refresh_interval: 5
  
  # 资源阈值告警
  thresholds:
    cpu_warning: 80      # CPU使用率告警阈值（%）
    cpu_critical: 90     # CPU使用率严重告警阈值（%）
    memory_warning: 80   # 内存使用率告警阈值（%）
    memory_critical: 90  # 内存使用率严重告警阈值（%）
    disk_warning: 80     # 磁盘使用率告警阈值（%）
    disk_critical: 90    # 磁盘使用率严重告警阈值（%）

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/tx-k8s-manager.log"
  max_size: "10MB"
  backup_count: 5
